export default {
  virtualSummary: {
    description:
      "Interfaz funcional diseñada específicamente para dispositivos virtuales, que proporciona una experiencia de operación limpia y eficiente",
    analogTitle: "Gestión de Analógicos",
    analogDesc: "Configuración, monitoreo y gestión de parámetros analógicos",
    digitalInputTitle: "Monitoreo de Entradas Digitales",
    digitalInputDesc: "Monitoreo en tiempo real y visualización del estado de entradas digitales",
    digitalOutputTitle: "Control de Salidas Digitales",
    digitalOutputDesc: "Operaciones de control y gestión de estado para salidas digitales",
    faultTitle: "Señales de Fallas",
    faultDesc: "Monitoreo y análisis de diagnóstico de señales de fallas",
    ledTitle: "Parámetros LED",
    ledDesc: "Configuración y gestión de parámetros de indicadores LED",
    waveTitle: "Reproducción de Fallas",
    waveDesc: "Reproducción y análisis de archivos de ondas de fallas",
    analogParam: {
      title: "Parámetros Analógicos",
      description: "Configuración y gestión de parámetros analógicos"
    },
    digitalInput: {
      title: "Entradas Digitales",
      description: "Monitoreo en tiempo real de entradas digitales"
    },
    digitalOutput: {
      title: "Salidas Digitales",
      description: "Control y gestión de salidas digitales"
    },
    faultParam: {
      title: "Parámetros de Fallas",
      description: "Configuración y diagnóstico de fallas"
    },
    ledParam: {
      title: "Parámetros LED",
      description: "Configuración de indicadores LED"
    },
    waveReplay: {
      title: "Reproducción de Ondas",
      description: "Análisis de archivos de ondas"
    },
    advantages: "Ventajas Clave",
    advantage1: "No se requiere dispositivo físico, reduciendo costos y riesgos",
    advantage2: "Interfaz funcional simplificada, mejorando la eficiencia operativa",
    advantage3: "Conexión rápida sin esperar la carga de archivos XML",
    advantage4: "Menú funcional unificado que garantiza una experiencia consistente",
    usage: "Instrucciones de Uso",
    usageDescription:
      "Haga clic en el menú funcional a la izquierda para acceder directamente a la interfaz funcional del dispositivo virtual correspondiente. Todas las funciones se han optimizado en base al marco original para proporcionar una experiencia de operación más profesional y eficiente."
  },

  configure: {
    remoteSet: "Teleajuste"
  },
  console: {
    title: "Consola",
    clear: "Limpiar",
    selectAll: "Seleccionar todo",
    copy: "Copiar",
    copySuccess: "Copiado con éxito",
    noTextSelected: "No hay texto seleccionado",
    copyFailed: "Error al copiar",
    clearSuccess: "Consola limpiada",
    collapse: "Colapsar",
    expand: "Expandir"
  },
  groupInfo: {
    title: "Información de Grupo",
    virtualTitle: "Funciones del Dispositivo Virtual",
    table: {
      id: "Índice",
      name: "Nombre",
      desc: "Descripción",
      fc: "FC",
      count: "Cantidad",
      method: "Método",
      keyword: "Palabra clave",
      inherit: "Heredar"
    },
    messages: {
      fetchDataError: "Error al obtener datos",
      fetchedData: "Datos obtenidos:",
      fetchCustomGroupFailed: "Error al obtener información del grupo personalizado"
    }
  },
  treeClickLog: "Clic en treeClick : ",
  contentView: "Vista de contenido",
  emptyDeviceId: "El id del dispositivo actual está vacío",
  invalidResponseStructure: "Estructura de respuesta inválida",
  formattedMenuDataLog: "Datos de menú formateados ===",
  allSettings: "Todos los valores",
  allEditSpSettings: "Todos los valores de zona única",
  allEditSgSettings: "Todos los valores de zona múltiple",
  deviceTreeDataLog: "Datos del árbol de dispositivos",
  failedToLoadMenu: "Error al cargar el menú del dispositivo:",
  innerTabs: {
    contentView: "Contenido",
    fileUpload: "Subir",
    fileDownload: "Bajar",
    deviceTime: "Sincro",
    deviceOperate: "Dispositivo",
    variableDebug: "Debug",
    oneClickBackup: "Respaldo",
    entryConfig: "Entradas",
    tabClickLog: "Pestaña:"
  },
  devices: {
    notConnectedAlt: "Dispositivo no conectado",
    pleaseConnect: "¡Por favor conecte el dispositivo primero!"
  },
  list: {
    unnamedDevice: "Dispositivo sin nombre",
    connected: "Conectado",
    disconnected: "Desconectado",
    connect: "Conectado",
    edit: "Editar",
    disconnect: "Desconectado",
    remove: "Eliminar",
    noDeviceFound: "No se encontró el dispositivo",
    handleClickLog: "Clic en handleListClick:",
    disconnectBeforeEdit: "Por favor, primero desconéctese para editar",
    connectSuccess: "Dispositivo {name}: Conexión exitosa",
    connectExist: "Dispositivo {name}: Conexión ya existe",
    connectFailed: "Dispositivo {name}: Conexión fallida",
    connectFailedReason: "Razón de falla de conexión del dispositivo:",
    disconnectedSuccess: "Dispositivo {name}: Desconectado",
    disconnectedNotify: "Dispositivo {name} conexión desconectada",
    currentDisconnectedNotify: "Conexión del dispositivo actual desconectada",
    operateFailed: "Dispositivo {name}: Operación fallida",
    disconnectBeforeDelete: "Por favor, primero desconéctese para eliminar",
    dataLog: "Datos:",
    ipPortExist: "El IP y el puerto ya existen, no repita la adición",
    messageMonitor: "Monitor de Mensajes",
    connectFirst: "Por favor conecte el dispositivo primero",
    messageMonitorOpened: "Dispositivo {name}: monitor de mensajes abierto",
    deviceAlreadyConnected: "El dispositivo ya está conectado, por favor desconéctelo primero antes de conectar"
  },
  messageMonitor: {
    title: "Monitor de Mensajes",
    start: "Iniciar Monitoreo",
    stop: "Detener Monitoreo",
    clear: "Limpiar",
    export: "Exportar",
    expand: "Expandir",
    collapse: "Contraer",
    close: "Cerrar",
    messageType: "Mensaje",
    noMessages: "Sin datos de mensajes",
    noMessagesToExport: "Sin datos de mensajes para exportar",
    startSuccess: "Monitoreo de mensajes iniciado",
    stopSuccess: "Monitoreo de mensajes detenido",
    stopSuccessWithDevice: "Dispositivo {name}: Monitoreo de mensajes detenido",
    clearSuccess: "Mensajes limpiados exitosamente",
    exportSuccess: "Mensajes exportados exitosamente",
    exportFailed: "Error al exportar mensajes",
    toggleFailed: "Error al cambiar estado de monitoreo",
    toggleFailedWithDevice: "Dispositivo {name}: Error al cambiar estado de monitoreo",
    pauseScroll: "Pausar Desplazamiento",
    resumeScroll: "Reanudar Desplazamiento",
    monitoring: "Monitoreando",
    copy: "Copiar",
    copySuccess: "Mensaje copiado al portapapeles",
    copyFailed: "Error al copiar",
    autoScrollEnabled: "Desplazamiento automático habilitado",
    autoScrollDisabled: "Desplazamiento automático pausado",
    send: "Enviar",
    receive: "Recibir",
    message: "Mensaje"
  },
  search: {
    placeholder: "Buscar dispositivo",
    ipPortExist: "El IP y el puerto ya existen, no repita la adición"
  },
  summaryPie: {
    other: "Otro",
    title: "Proporción",
    subtext: "Distribución de valores"
  },
  deviceInfo: {
    title: "Información del dispositivo",
    export: "Exportar",
    exportTitle: "Exportar información del dispositivo",
    exportLoading: "Exportando información básica del dispositivo...",
    exportSuccess: "Exportación de información básica del dispositivo exitosa",
    exportFailed: "Exportación de información básica del dispositivo fallida",
    getInfoFailed: "Error al obtener información del dispositivo. Razón: {msg}",
    getInfoFailedEmpty: "Error al obtener información del dispositivo. Razón: Datos vacíos!",
    defaultFileName: "Información del dispositivo.xlsx",
    confirm: "Confirmar",
    tip: "Sugerencia"
  },
  allParamSetting: {
    title: "Todos los valores",
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    groupTitle: "Grupo de valores:",
    allGroups: "Todos",
    noDataToImport: "No hay datos para importar",
    importSuccess: "Importación de valores exitosa",
    importFailed: "Importación de valores fallida: {msg}",
    requestFailed: "Solicitud fallida, por favor intente más tarde",
    queryFailed: "Consulta de valores fallida: {msg}",
    unsavedChanges: "Existen cambios no guardados, ¿desea continuar actualizando?",
    confirmButton: "Confirmar",
    cancelButton: "Cancelar",
    alertTitle: "Sugerencia",
    errorTitle: "Error",
    noDataToConfirm: "No hay datos para confirmar",
    confirmSuccess: "Actualización de valores exitosa",
    confirmFailed: "Actualización de valores fallida: ",
    responseLog: "Datos de respuesta:",
    continueAutoRefresh: "Continuar actualización automática",
    settingGroup: "Grupo de valores",
    all: "Todos",
    minValue: "Valor mínimo",
    maxValue: "Valor máximo",
    step: "Paso",
    unit: "Unidad",
    searchNamePlaceholder: "Ingrese el nombre del valor para buscar",
    searchDescPlaceholder: "Ingrese la descripción del valor para buscar",
    autoRefreshWarning: "No se permite modificar datos cuando la actualización automática está habilitada",
    invalidValue: "El valor {name} del valor {value} no está en el rango válido",
    exportFileName: "Parámetros de valores del dispositivo_Todos los valores.xlsx",
    selectPathLog: "Seleccionar ruta: ",
    exportSuccess: "Exportar lista de valores exitosa"
  },
  variable: {
    autoRefresh: "Actualización automática",
    variableName: "Nombre de variable",
    inputVariableName: "Ingrese el nombre de la variable para agregar",
    refresh: "Actualizar",
    add: "Agregar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    delete: "Eliminar",
    noDataToConfirm: "No hay datos para confirmar",
    warning: "Alerta",
    variableModifiedSuccess: "Modificación de variable exitosa",
    variableModifiedFailed: "Modificación de variable fallida, razón:",
    requestFailed: "Solicitud fallida, por favor intente más tarde",
    error: "Error",
    success: "Éxito",
    variableAddSuccess: "Variable de dispositivo de depuración agregada exitosamente",
    variableAddFailed: "Variable de dispositivo de depuración agregada fallida, razón:",
    variableDeleteSuccess: "Variable de dispositivo de depuración eliminada exitosamente",
    variableDeleteFailed: "Variable de dispositivo de depuración eliminada fallida, razón:",
    exportSuccess: "Exportación de información de depuración de dispositivo exitosa",
    exportFailed: "Exportación de información de depuración de dispositivo fallida, razón:",
    importSuccess: "Importación de información de depuración de dispositivo exitosa",
    importFailed: "Importación de información de depuración de dispositivo fallida:",
    confirmRefresh: "Existen cambios no guardados, ¿desea continuar actualizando?",
    confirmAutoRefresh: "Existen cambios no guardados, ¿desea continuar actualizando automáticamente?",
    pleaseInputVariableName: "Por favor, ingrese el nombre de la variable",
    exportTitle: "Exportar información de depuración de dispositivo",
    importTitle: "Importar información de depuración de dispositivo",
    defaultExportPath: "Información de depuración de dispositivo.xlsx",
    title: "Depuración de variable",
    variableNamePlaceholder: "Ingrese el nombre de la variable para agregar",
    batchDelete: "Eliminar por lotes",
    modifySuccess: "Modificación de variable exitosa",
    modifyFailed: "Modificación de variable fallida, razón: {msg}",
    alertTitle: "Alerta",
    successTitle: "Sugerencia",
    errorTitle: "Error",
    confirmButton: "Confirmar",
    cancelButton: "Cancelar",
    sequence: "Índice",
    name: "Nombre",
    value: "Valor",
    type: "Tipo",
    description: "Descripción",
    address: "Dirección",
    operation: "Operación",
    enterVariableName: "Ingrese el nombre de la variable para agregar",
    responseLog: "Datos de respuesta:",
    addSuccess: "Variable de dispositivo de depuración agregada exitosamente",
    addFailed: "Variable de dispositivo de depuración agregada fallida, razón:",
    addFailedWithName: "Variable {name} agregada fallida: {reason}",
    exportFileName: "Información de depuración de dispositivo.xlsx",
    selectPathLog: "Seleccionar ruta:",
    exportSuccessLog: "Exportación de información de depuración de dispositivo exitosa, {path}",
    exportFailedLog: "Exportación de información de depuración de dispositivo fallida, razón:",
    importFailedLog: "Importación de información de depuración de dispositivo fallida:",
    unsavedChanges: "Existen cambios no guardados, ¿desea continuar actualizando?",
    continueAutoRefresh: "Continuar actualización automática",
    tip: "Sugerencia",
    sequenceNumber: "Índice"
  },
  virtualParam: {
    autoRefresh: "Actualización automática",
    name: "Nombre",
    description: "Descripción",
    dataValue: "Amplitud",
    ang: "Fase",
    value: "Valor",
    sequenceNumber: "Índice",
    operation: "Operación",
    inputName: "Ingrese nombre para buscar",
    inputDescription: "Ingrese descripción para buscar",
    refresh: "Actualizar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    warning: "Advertencia",
    success: "Éxito",
    error: "Error",
    cancel: "Cancelar",
    noDataToConfirm: "No hay datos para confirmar",
    paramModifiedSuccess: "Parámetro modificado exitosamente",
    paramModifiedFailed: "Fallo en la modificación del parámetro: ",
    requestFailed: "Solicitud fallida, por favor intente más tarde",
    errorTitle: "Error",
    invalidNumber: "Número inválido: {value}",
    invalidValue: "Valor inválido: {value}",
    autoRefreshEditForbidden: "La edición está prohibida en modo de actualización automática",
    confirmRefresh: "Hay cambios no guardados, ¿continuar actualizando?",
    confirmAutoRefresh: "Hay cambios no guardados, ¿continuar con la actualización automática?",
    continueAutoRefresh: "Continuar actualización automática",
    exportTitle: "Exportar parámetros de dispositivo virtual",
    importTitle: "Importar parámetros de dispositivo virtual",
    defaultExportPath: "Parámetros de dispositivo virtual.xlsx",
    exportSuccess: "Exportación exitosa: ",
    exportFailed: "Fallo en la exportación",
    importSuccess: "Importación exitosa",
    importFailed: "Fallo en la importación",
    loadDemo: "Cargar datos de ejemplo",
    exitDemo: "Salir de los datos de ejemplo"
  },
  virtualWaveReplay: {
    title: "Interfaz de prueba de reproducción de forma de onda de falla",
    description: "Esta es una interfaz de prueba simple",
    testFunction: "Funciones de prueba",
    testButton: "Botón de prueba",
    showMessage: "Mostrar mensaje",
    clearData: "Limpiar datos",
    testData: "Datos de prueba",
    testStatus: "Estado de prueba",
    testTime: "Tiempo de prueba",
    statusPending: "Prueba pendiente",
    statusTesting: "Probando...",
    statusComplete: "Prueba completa",
    testComplete: "Prueba completa",
    testMessage: "Este es un mensaje de prueba",
    dataCleared: "Datos limpiados"
  },
  ledColors: {
    R: "Rojo",
    G: "Verde",
    B: "Azul",
    Y: "Amarillo",
    O: "Naranja",
    P: "Púrpura",
    W: "Blanco",
    C: "Cian",
    M: "Magenta",
    OFF: "Apagado",
    ON: "Encendido",
    UNKNOWN: "Desconocido"
  },
  backup: {
    sequence: "Índice",
    title: "Copia de seguridad del dispositivo",
    savePath: "Ruta de guardado",
    setPath: "Establecer ruta de guardado",
    setPathTitle: "Establecer ruta",
    startBackup: "Iniciar copia de seguridad",
    cancelBackup: "Cancelar copia de seguridad",
    backup: "Copia de seguridad",
    backupType: "Tipo de copia de seguridad",
    progress: "Progreso",
    status: "Estado",
    operation: "Operación",
    backupTypes: {
      paramValue: "Valores de parámetros del dispositivo",
      faultInfo: "Informes de fallos del dispositivo",
      cidConfigPrjLog: "CID/CCD/Configuración del dispositivo/Información de depuración/PRJ/Registro",
      waveReport: "Archivos de onda del dispositivo"
    },
    backupDesc: "Descripción del contenido de la copia de seguridad",
    backupDescTypes: {
      paramValue: "Exportar valores de parámetros del dispositivo (param export.xlsx)",
      faultInfo: "Exportar información de fallos del dispositivo (informes de evento/operación/fallo/auditoría)",
      cidConfigPrjLog: "Exportar archivos de configuración (CID/CCD, configuración XML, archivos de registro)",
      waveReport: "Exportar archivos de onda del dispositivo (/wave/comtrade)"
    },
    locateFolder: "Ubicar carpeta",
    backupSuccess: "Copia de seguridad exitosa",
    openFolderFailed: "Error al abrir la carpeta",
    backupFailed: "Copia de seguridad fallida",
    noTypeSelected: "Por favor, seleccione primero el tipo de copia de seguridad",
    cancelSuccess: "Cancelación exitosa",
    cancelFailed: "Error al cancelar"
  },
  operate: {
    title: "Operación del dispositivo",
    manualWave: "Registrar onda manual",
    resetDevice: "Restablecer dispositivo",
    clearReport: "Limpiar informe",
    clearWave: "Limpiar registro de onda",
    executing: "Ejecutando...",
    selectOperation: "Por favor, seleccione la operación",
    success: {
      manualWave: "Registro de onda manual exitoso",
      resetDevice: "Restablecimiento de dispositivo exitoso",
      clearReport: "Limpiar informe exitoso",
      clearWave: "Limpiar registro de onda exitoso"
    },
    fail: {
      manualWave: "Registro de onda manual fallido, razón:",
      resetDevice: "Restablecimiento de dispositivo fallido, razón:",
      clearReport: "Limpiar informe fallido, razón:",
      clearWave: "Limpiar registro de onda fallido, razón:"
    }
  },
  time: {
    title: "Sincronización de tiempo del dispositivo",
    currentTime: "Tiempo actual",
    deviceTime: "Tiempo del dispositivo",
    selectDateTime: "Seleccionar fecha y hora",
    milliseconds: "Milisegundos",
    now: "Ahora",
    read: "Leer",
    write: "Escribir",
    readSuccess: "Leer tiempo del dispositivo exitoso.",
    readFailed: "Error al leer tiempo del dispositivo: {msg}",
    readFailedInvalidFormat: "Error al leer tiempo del dispositivo: Formato de tiempo no válido",
    readFailedDataError: "Error al leer tiempo del dispositivo: Error de formato de datos de tiempo",
    writeSuccess: "Escribir tiempo del dispositivo exitoso.",
    writeFailed: "Error al escribir tiempo del dispositivo: {msg}",
    writeFailedInvalidFormat: "Error al escribir tiempo del dispositivo: Formato de tiempo no válido",
    millisecondsRangeError: "El rango de milisegundos debe estar entre 0-999",
    unknownError: "Error desconocido"
  },
  reportOperate: {
    title: "Operación de informe",
    date: "Fecha:",
    search: "Buscar",
    save: "Guardar",
    clearList: "Limpiar lista",
    loading: "Cargando datos",
    progress: {
      title: "Información de progreso",
      loading: "Cargando",
      searching: "Buscando {type}"
    },
    table: {
      reportId: "Número de informe",
      name: "Nombre",
      time: "Tiempo",
      operationAddress: "Dirección de operación",
      operationParam: "Parámetro de operación",
      value: "Valor",
      step: "Paso",
      source: "Fuente",
      sourceType: "Tipo de fuente",
      result: "Resultado"
    },
    messages: {
      selectDateRange: "Por favor, seleccione un rango de fecha completo",
      noDataToSave: "No hay datos para guardar",
      saveSuccess: "Guardar exitoso",
      saveReport: "Guardar informe"
    }
  },
  reportGroup: {
    title: "Grupo de informe",
    date: "Fecha:",
    search: "Buscar",
    save: "Guardar",
    clearList: "Limpiar lista",
    autoRefresh: "Actualización automática",
    loading: "Cargando datos",
    progress: {
      title: "Información de progreso",
      loading: "Cargando",
      searching: "Buscando {type}"
    },
    table: {
      reportId: "Número de informe",
      time: "Tiempo",
      description: "Descripción"
    },
    contextMenu: {
      uploadWave: "Llamar onda",
      getHistoryReport: "Obtener informe histórico",
      saveResult: "Guardar resultado",
      clearContent: "Limpiar contenido de la página"
    },
    messages: {
      selectDateRange: "Por favor, seleccione un rango de fecha completo",
      noDataToSave: "No hay datos para guardar",
      noFileToUpload: "No hay archivo para llamar",
      saveSuccess: "Guardar exitoso",
      saveReport: "Guardar informe",
      waveToolNotConfigured: "No se configuró la ruta de herramienta de onda de terceros",
      waveFileUploading: "Llamando archivo de onda",
      waveFileUploadComplete: "Llamado de archivo de onda completado",
      waveFileUploadCompleteWithPath: "Llamado de archivo de onda completado, ruta: {path}",
      openWaveFileConfirm: "¿Desea abrir el archivo de onda con la herramienta de terceros?",
      openWaveFileTitle: "Sugerencia de precaución",
      confirm: "Confirmar",
      cancel: "Cancelar"
    },
    refresh: {
      stop: "Detener actualización",
      start: "Actualizar automáticamente"
    },
    hiddenItems: {
      show: "Mostrar elementos ocultos",
      hide: "No mostrar elementos ocultos"
    }
  },
  fileDownload: {
    title: "Descarga de archivo",
    deviceDirectory: "Directorio del dispositivo",
    reboot: "Reiniciar",
    noReboot: "No reiniciar",
    selectFile: "Seleccionar archivo",
    addDownloadFile: "Agregar archivo para descargar",
    addDownloadFolder: "Agregar carpeta para descargar",
    addDownloadFilesAndFolders: "Agregar archivos y carpetas",
    downloadFile: "Descargar archivo",
    cancelDownload: "Cancelar descarga",
    importList: "Importar lista",
    exportList: "Exportar lista",
    batchDelete: "Eliminar por lotes",
    clearList: "Limpiar lista",
    download: "Descargar",
    delete: "Eliminar",
    fileName: "Nombre de archivo",
    fileSize: "Tamaño de archivo",
    filePath: "Ruta de archivo",
    lastModified: "Última modificación",
    progress: "Progreso",
    status: "Estado",
    operation: "Operación",
    folder: "Carpeta",
    waitingDownload: "Esperando descarga",
    calculatingFileInfo: "Calcular información de archivo",
    downloadPreparing: "Preparando descarga",
    downloading: "Descargando......",
    downloadComplete: "Descarga completada",
    downloadError: "Error al descargar:",
    userCancelled: "Usuario canceló",
    allFilesComplete: "Descarga completada",
    fileExists: "El archivo {path} ya existe, no se puede agregar",
    selectValidFile: "Por favor, seleccione un archivo válido para la operación de descarga",
    remotePathEmpty: "La ruta remota no puede estar vacía",
    noDownloadTask: "No se pudo obtener la tarea de descarga para cancelar",
    fileSizeZero: "El archivo {fileName} tiene tamaño 0, no se puede descargar",
    downloadCancelled: "Descarga cancelada de archivo {path} completada",
    downloadCancelledFailed: "Descarga cancelada de archivo {path} fallida, razón: {msg}",
    fileDeleted: "Archivo {path} eliminado completamente",
    exportSuccess: "Exportación de lista de descarga de archivo exitosa",
    exportFailed: "Exportación de lista de descarga de archivo fallida",
    importSuccess: "Importación de lista de descarga de archivo exitosa",
    importFailed: "Importación de lista de descarga de archivo fallida: {msg}",
    downloadList: "Lista de archivos de descarga",
    exportTitle: "Exportar lista de archivos de descarga",
    importTitle: "Importar lista de archivos de descarga",
    error: "Error",
    tip: "Aviso",
    confirm: "Confirmar",
    sequence: "Índice",
    confirmButton: "Confirmar",
    cancelButton: "Cancelar",
    alertTitle: "Aviso",
    errorTitle: "Error",
    successTitle: "Éxito",
    warningTitle: "Advertencia",
    loading: "Cargando",
    executing: "Ejecutando...",
    noData: "Sin datos",
    selectDateRange: "Por favor seleccione el rango de fechas",
    search: "Buscar",
    save: "Guardar",
    clear: "Limpiar",
    refresh: "Actualizar",
    stop: "Detener",
    start: "Iniciar",
    show: "Mostrar",
    hide: "Ocultar",
    showHiddenItems: "Mostrar elementos ocultos",
    hideHiddenItems: "Ocultar elementos",
    continue: "Continuar",
    cancel: "Cancelar",
    confirmImport: "Confirmar importación",
    confirmExport: "Confirmar exportación",
    confirmDelete: "Confirmar eliminación",
    confirmClear: "Confirmar limpieza",
    confirmCancel: "Confirmar cancelación",
    confirmContinue: "Confirmar continuación",
    confirmStop: "Confirmar detención",
    confirmStart: "Confirmar inicio",
    confirmShow: "Confirmar mostrar",
    confirmHide: "Confirmar ocultar",
    confirmRefresh: "Confirmar actualización",
    confirmSave: "Confirmar guardado",
    confirmSearch: "Confirmar búsqueda",
    confirmClearList: "Confirmar limpieza de lista",
    confirmImportList: "Confirmar importación de lista",
    confirmExportList: "Confirmar exportación de lista",
    confirmBatchDelete: "Confirmar eliminación por lotes",
    confirmDownload: "Confirmar descarga",
    confirmCancelDownload: "Confirmar cancelación de descarga",
    confirmDeleteFile: "Confirmar eliminación de archivo",
    confirmClearFiles: "Confirmar limpieza de archivos",
    confirmImportFiles: "Confirmar importación de archivos",
    confirmExportFiles: "Confirmar exportación de archivos",
    confirmBatchDeleteFiles: "Confirmar eliminación por lotes de archivos",
    confirmDownloadFiles: "Confirmar descarga de archivos",
    confirmCancelDownloadFiles: "Confirmar cancelación de descarga de archivos",
    rename: "Renombrar para descarga",
    renamePlaceholder: "Renombrar al descargar (opcional)",
    renameCopyFailed: "Error al copiar archivo para renombrar:",
    packageProgram: "Empaquetado de programa",
    selectSaveDir: "Seleccionar directorio de guardado",
    packageBtn: "Empaquetar",
    locateDir: "Ubicar carpeta",
    saveDirEmpty: "¡Por favor seleccione primero el directorio de guardado!",
    packageSuccess: "¡Empaquetado de programa completado!",
    packageFailed: "Error de empaquetado: {msg}",
    noFileSelected: "¡Por favor seleccione los archivos a empaquetar!",
    zipPath: "Ruta del archivo ZIP: {zipPath}",
    addToDownload: "Agregar a descarga",
    fileAdded: "Archivo agregado a la lista de descarga: {path}",
    rebootSuccess: "Reinicio del dispositivo exitoso",
    rebootFailed: "Reinicio del dispositivo fallido: {msg}"
  },
  fileUpload: {
    serialNumber: "Índice",
    title: "Subida de archivo",
    importList: "Importar lista",
    exportList: "Exportar lista",
    batchDelete: "Eliminar por lotes",
    clearList: "Limpiar lista",
    upload: "Subir",
    cancelUpload: "Cancelar subida",
    delete: "Eliminar",
    sequence: "Índice",
    fileName: "Nombre de archivo",
    fileSize: "Tamaño de archivo",
    filePath: "Ruta de archivo",
    lastModified: "Última modificación",
    progress: "Progreso",
    statusTitle: "Estado",
    status: {
      waiting: "Esperando subir",
      preparing: "Preparando subir",
      uploading: "Subiendo......",
      completed: "Subida completada",
      error: "Error al subir:",
      cancelled: "Usuario canceló"
    },
    operation: "Operación",
    calculatingFileInfo: "Calcular información de archivo",
    uploadPreparing: "Preparando subir",
    uploading: "Subiendo......",
    uploadComplete: "Subida completada",
    uploadError: "Error al subir: {errorMsg}",
    userCancelled: "Usuario canceló",
    allFilesComplete: "Subida completada",
    fileExists: "El archivo {path} ya existe, no se puede agregar",
    invalidFile: "Por favor, seleccione un archivo válido para la operación de subida",
    emptySavePath: "La ruta de guardado de archivo no puede estar vacía",
    fileUploadComplete: "Archivo {fileName} subido completamente",
    selectPath: "Seleccionar ruta",
    pathOptions: {
      shr: "/shr",
      configuration: "/shr/configuration",
      log: "/log",
      wave: "/wave",
      comtrade: "/wave/comtrade"
    },
    deviceDirectory: "Directorio del dispositivo",
    savePath: "Ruta de guardado",
    setPath: "Establecer ruta",
    getFiles: "Obtener archivos",
    uploadFiles: "Subir archivos",
    errors: {
      invalidFile: "Por favor, seleccione un archivo válido para la operación de subida",
      emptySavePath: "La ruta de guardado de archivo no puede estar vacía",
      noUploadTask: "No se pudo obtener la tarea de subida para cancelar",
      getFilesFailed: "Error al obtener archivos del directorio del dispositivo",
      fileSizeZero: "El archivo {fileName} tiene tamaño 0, no se puede subir"
    },
    messages: {
      uploadCompleted: "Archivo subido completamente",
      uploadCancelled: "Subida cancelada completamente",
      clearListSuccess: "Lista de archivos limpiada exitosamente"
    }
  },
  info: {
    title: "Información del dispositivo",
    export: "Exportar",
    exportSuccess: "Exportación de información básica del dispositivo exitosa",
    exportFailed: "Exportación de información básica del dispositivo fallida",
    exportTip: "Sugerencia",
    confirm: "Confirmar",
    exportLoading: "Exportando información básica del dispositivo...",
    getInfoFailed: "Error al obtener información del dispositivo. Razón:",
    dataEmpty: "Datos vacíos!"
  },
  summary: {
    title: "Resumen de grupo de dispositivos",
    basicInfo: "Info básica",
    settingTotal: "Total de valores",
    telemetry: "Telemetría",
    teleindication: "Teleindicación",
    telecontrol: "Telecontrol",
    driveOutput: "Salida de accionamiento",
    settingRatio: "Proporción de valores"
  },
  dict: {
    refresh: "Actualizar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    sequence: "Índice",
    shortAddress: "Dirección corta",
    shortAddressTooltip: "Ingrese la dirección corta para buscar",
    chinese: "Chino",
    english: "Inglés",
    spanish: "Español",
    french: "Francés",
    operation: "Operación",
    confirmLog: "Confirmar diccionario",
    importLog: "Importar diccionario",
    exportLog: "Exportar diccionario",
    refreshLog: "Actualizar diccionario",
    newValueLog: "Nuevo valor:"
  },
  allParamCompare: {
    title: "Comparar diferencia de importación de todos los valores",
    cancel: "Cancelar",
    confirm: "Confirmar importación",
    groupName: "Nombre de grupo",
    name: "Nombre",
    description: "Descripción",
    minValue: "Valor mínimo",
    maxValue: "Valor máximo",
    step: "Paso",
    unit: "Unidad",
    address: "Dirección",
    oldValue: "Valor antiguo",
    newValue: "Nuevo valor",
    sequence: "Índice",
    searchName: "Ingrese el nombre del valor para buscar",
    searchDescription: "Ingrese la descripción del valor para buscar",
    messages: {
      noSelection: "No se seleccionó ningún dato",
      error: "Error"
    }
  },
  error: {
    formInitFailed: "Error en la inicialización del formulario, inténtelo de nuevo",
    enterMenuName: "Por favor, introduzca el nombre del menú",
    enterMenuDesc: "Por favor, introduzca la descripción del menú",
    completeForm: "Por favor, complete el formulario",
    editFailed: "Error al editar: {msg}",
    createFailed: "Error al crear: {msg}",
    deleteFailed: "Error al eliminar: {msg}",
    enterReportName: "Por favor, introduzca el nombre del informe",
    enterReportDesc: "Por favor, introduzca la descripción del informe",
    selectInherit: "Por favor, seleccione el campo heredado",
    incompleteData: "Datos incompletos, compruebe el nombre y la descripción del grupo",
    selectAtLeastOnePoint: "Por favor, seleccione al menos un punto",
    createPointGroupUnderCustomMenu: "Los grupos de puntos personalizados solo pueden crearse bajo nodos de menú personalizados",
    createFailedRetry: "Error al crear, inténtelo de nuevo",
    editModeOnlyForPointGroup: "El modo de edición solo está disponible para nodos de grupo de puntos",
    parentMenuNotFound: "No se encontró información del menú principal",
    editFailedRetry: "Error al editar, inténtelo de nuevo",
    operationFailed: "Error en la operación: {msg}"
  },
  deviceForm: {
    title: {
      add: "Agregar dispositivo",
      edit: "Editar dispositivo"
    },
    name: "Nombre del dispositivo",
    ip: "IP",
    port: "Puerto",
    connectTimeout: "Tiempo de conexión (milisegundos)",
    readTimeout: "Tiempo de solicitud global (milisegundos)",
    paramTimeout: "Tiempo de modificación de valor (milisegundos)",
    encrypted: "Conexión cifrada",
    isVirtual: "Dispositivo virtual",
    advanced: {
      show: "Mostrar opciones avanzadas",
      hide: "Ocultar opciones avanzadas"
    },
    buttons: {
      cancel: "Cancelar",
      confirm: "Confirmar"
    },
    messages: {
      nameRequired: "Por favor, ingrese el nombre del dispositivo",
      nameTooLong: "El nombre del dispositivo no debe ser demasiado largo",
      invalidIp: "Por favor, ingrese una IP válida",
      invalidPort: "El puerto debe estar entre 1-65535",
      timeoutTooShort: "El tiempo de espera no debe ser demasiado corto"
    }
  },
  paramCompare: {
    title: "Comparar diferencia de importación de valor",
    cancel: "Cancelar",
    confirm: "Confirmar importación",
    sequence: "Índice",
    name: "Nombre",
    description: "Descripción",
    minValue: "Valor mínimo",
    maxValue: "Valor máximo",
    step: "Paso",
    unit: "Unidad",
    address: "Dirección",
    oldValue: "Valor antiguo",
    newValue: "Nuevo valor",
    searchName: "Ingrese el nombre del valor para buscar",
    searchDescription: "Ingrese la descripción del valor para buscar",
    messages: {
      noSelection: "No se seleccionó ningún dato",
      error: "Error"
    }
  },
  progress: {
    title: "Información de progreso",
    executing: "Ejecutando..."
  },
  remoteYm: {
    title: "Remoto de impulso",
    sequence: "Índice",
    shortAddress: "Dirección corta",
    description: "Descripción",
    value: "Valor",
    operation: "Operación",
    inputShortAddressFilter: "Ingrese la dirección corta para filtrar",
    inputDescriptionFilter: "Ingrese la descripción para filtrar",
    invalidData: "Valor {name} del valor {value} no válido",
    error: "Error",
    success: "Éxito",
    executeSuccess: "Ejecución exitosa",
    prompt: "Aviso",
    confirmButton: "Confirmar",
    confirmExecute: "Confirmar ejecución",
    confirm: "Confirmar",
    executeButton: "Ejecutar",
    cancelButton: "Cancelar"
  },
  remoteYt: {
    title: "Remoto de ajuste",
    sequence: "Índice",
    directControl: "Control directo",
    selectControl: "Control de selección",
    shortAddress: "Dirección corta",
    description: "Descripción",
    value: "Valor",
    operation: "Operación",
    inputShortAddressFilter: "Ingrese la dirección corta para filtrar",
    inputDescriptionFilter: "Ingrese la descripción para filtrar",
    invalidData: "Valor {name} del valor {value} no válido",
    error: "Error",
    success: "Éxito",
    executeSuccess: "Ejecución exitosa",
    prompt: "Aviso",
    confirm: "Confirmar",
    errorInfo: "Información de error",
    executeFailed: "Fallo en la ejecución del telecontrol remoto, razón: {msg}",
    executeSuccessLog: "{desc} Ejecución del telecontrol remoto exitosa",
    cancelSuccess: "Cancelación exitosa",
    cancelFailed: "Fallo en la cancelación del telecontrol remoto, razón: {msg}",
    selectSuccess: "Selección exitosa, ¿ejecutar?",
    confirmInfo: "Información de confirmación",
    execute: "Ejecutar",
    cancel: "Cancelar"
  },
  paramSetting: {
    title: "Valor de parámetro del dispositivo",
    queryFailed: "Error en la consulta de valores: {msg}",
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    confirm: "Confirmar",
    import: "Importar",
    export: "Exportar",
    currentEditArea: "Área de operación actual",
    selectEditArea: "Área de edición actual",
    noDataToImport: "No hay datos para importar",
    noDataToConfirm: "No hay datos para confirmar",
    importSuccess: "Importación de valor exitosa",
    importFailed: "Importación de valor fallida",
    updateSuccess: "Actualización de valor exitosa",
    updateFailed: "Actualización de valor fallida",
    requestFailed: "Solicitud fallida, por favor intente más tarde",
    setEditArea: "Establecer",
    setEditAreaTitle: "Establecer área de edición",
    setEditAreaSuccess: "Área de edición establecida exitosamente",
    modifiedWarning: "Existen cambios no guardados, ¿desea continuar actualizando?",
    autoRefreshWarning: "Existen cambios no guardados, ¿desea continuar actualizando automáticamente?",
    autoRefreshDisabled: "No se permite modificar datos cuando la actualización automática está habilitada",
    invalidValue: "Valor {name} del valor {value} no válido",
    exportSuccess: "Exportación de valor de parámetro de dispositivo exitosa",
    exportFailed: "Exportación de valor de parámetro de dispositivo fallida",
    noDiffData: "No se obtuvo datos de diferencia",
    table: {
      index: "Índice",
      name: "Nombre",
      description: "Descripción",
      value: "Valor",
      minValue: "Valor mínimo",
      maxValue: "Valor máximo",
      step: "Paso",
      address: "Dirección",
      unit: "Unidad",
      operation: "Operación"
    },
    search: {
      namePlaceholder: "Ingrese el nombre del valor para buscar",
      descPlaceholder: "Ingrese la descripción del valor para buscar"
    }
  },
  remoteControl: {
    title: "Control remoto",
    sequence: "Índice",
    shortAddress: "Dirección corta",
    description: "Descripción",
    control: "Control de división/combinación",
    type: "Tipo",
    operation: "Operación",
    directControl: "Control directo",
    selectControl: "Control de selección",
    controlClose: "Control de división",
    controlOpen: "Control de combinación",
    noCheck: "No verificar",
    syncCheck: "Verificar sincronización",
    deadCheck: "Verificar sin presión",
    confirmInfo: "Confirmar información",
    execute: "Ejecutar",
    cancel: "Cancelar",
    confirm: "Confirmar",
    success: "Éxito",
    failed: "Fallo",
    errorInfo: "Información de error",
    promptInfo: "Información de sugerencia",
    confirmSuccess: "Selección exitosa, ¿ejecutar?",
    executeSuccess: "Ejecución exitosa",
    cancelSuccess: "Cancelación exitosa",
    executeFailed: "Ejecución de control remoto fallida, razón:",
    cancelFailed: "Cancelación de control remoto fallida, razón:",
    remoteExecuteSuccess: "Ejecución de control remoto exitosa",
    remoteCancelSuccess: "Cancelación de control remoto exitosa"
  },
  remoteDrive: {
    action: "Acción",
    executeSuccess: "Ejecución exitosa",
    executeFailed: "Ejecución fallida",
    prompt: "Información de sugerencia",
    error: "Información de error",
    confirm: "Confirmar",
    shortAddress: "Dirección corta",
    description: "Descripción",
    operation: "Operación",
    enterToFilter: "Ingrese la dirección corta para filtrar",
    enterToFilterDesc: "Ingrese la descripción para filtrar",
    actionSuccess: "Ejecución de acción exitosa",
    actionFailed: "Ejecución de acción fallida",
    failureReason: "Razón de falla",
    sequence: "Índice"
  },
  remoteSignal: {
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    export: "Exportar",
    sequence: "Índice",
    name: "Nombre",
    description: "Descripción",
    value: "Valor",
    quality: "Calidad",
    searchName: "Ingrese el nombre para buscar",
    searchDesc: "Ingrese la descripción para buscar",
    searchValue: "Ingrese el valor para buscar",
    exportTitle: "Exportar información de señal de dispositivo",
    exportSuccess: "Exportación de información de señal de dispositivo exitosa",
    exportFailed: "Exportación de información de señal de dispositivo fallida",
    exportSuccessWithPath: "Exportación de información de señal de dispositivo exitosa,",
    exportFailedWithError: "Exportación de información de señal de dispositivo fallida:",
    invalidData: "Datos no válidos:",
    errorInDataCallback: "Error en procesamiento de devolución de datos:",
    errorFetchingData: "Error al obtener datos:"
  },
  remoteTelemetry: {
    autoRefresh: "Actualización automática",
    refresh: "Actualizar",
    export: "Exportar",
    sequence: "Índice",
    name: "Nombre",
    description: "Descripción",
    value: "Valor",
    unit: "Unidad",
    quality: "Calidad",
    searchName: "Ingrese el nombre para buscar",
    searchDesc: "Ingrese la descripción para buscar",
    searchValue: "Ingrese el valor para buscar",
    exportTitle: "Exportar información de estado de dispositivo",
    exportSuccess: "Exportación de información de estado de dispositivo exitosa",
    exportFailed: "Exportación de información de estado de dispositivo fallida",
    exportSuccessWithPath: "Exportación de información de estado de dispositivo exitosa,",
    exportFailedWithError: "Exportación de información de estado de dispositivo fallida:",
    confirm: "Confirmar",
    tip: "Sugerencia",
    exportFileName: "Información de estado de dispositivo",
    selectPathLog: "Seleccionar ruta:"
  },
  remote: {
    directControl: "Control directo",
    selectControl: "Control de selección",
    serialNumber: "Índice",
    shortAddress: "Dirección corta",
    description: "Descripción",
    value: "Valor",
    operation: "Operación",
    inputShortAddressFilter: "Ingrese la dirección corta para filtrar",
    inputDescriptionFilter: "Ingrese la descripción para filtrar",
    invalidData: "Valor {name} del valor {value} no válido",
    error: "Error",
    success: "Éxito",
    executeSuccess: "Ejecución exitosa",
    prompt: "Información de sugerencia",
    confirm: "Confirmar",
    errorInfo: "Información de error",
    executeFailed: "Ejecución de ajuste fallida, razón: {msg}",
    executeSuccessLog: "{desc} Ajuste de ajuste exitoso",
    cancelSuccess: "Cancelación exitosa",
    cancelFailed: "Ejecución de ajuste fallida, razón: {msg}",
    selectSuccess: "Selección exitosa, ¿ejecutar?",
    confirmInfo: "Confirmar información",
    execute: "Ejecutar",
    cancel: "Cancelar"
  },
  report: {
    uploadWave: "Llamar onda",
    searchHistory: "Obtener informe histórico",
    saveResult: "Guardar resultado",
    clearContent: "Limpiar contenido de la página",
    date: "Fecha",
    query: "Buscar",
    save: "Guardar",
    autoRefresh: "Actualizar automáticamente",
    stopRefresh: "Detener actualización",
    clearList: "Limpiar lista",
    progressInfo: "Información de progreso",
    loading: "Cargando datos",
    reportNo: "Número de informe",
    time: "Tiempo",
    description: "Descripción",
    noFileToUpload: "No hay archivo para llamar",
    uploadSuccess: "Archivo de onda llamado completamente",
    uploadPath: "Archivo de onda llamado completamente, ruta:",
    noDataToSave: "No hay datos para guardar",
    saveSuccess: "Guardar exitoso",
    saveReport: "Guardar informe",
    openWaveConfirm: "¿Desea abrir el archivo de onda con la herramienta de terceros?",
    confirm: "Confirmar",
    cancel: "Cancelar",
    waveToolNotConfigured: "No se configuró la ruta de herramienta de onda de terceros",
    pleaseSelectTimeRange: "Por favor, seleccione un rango de fecha completo",
    querying: "Buscando",
    reportNumber: "Número de informe",
    operationAddress: "Dirección de operación",
    operationParams: "Parámetros de operación",
    result: "Resultado",
    progress: "Información de progreso",
    loadingText: "Cargando",
    selectCompleteTimeRange: "Por favor, seleccione un rango de fecha completo",
    fileUploading: "Llamando archivo de onda",
    fileUploadComplete: "Archivo llamado completamente"
  },
  customMenu: {
    addMenu: "Agregar menú personalizado",
    editMenu: "Editar menú personalizado",
    deleteMenu: "Eliminar menú personalizado",
    addReport: "Agregar informe personalizado",
    editReport: "Editar informe personalizado",
    deleteReport: "Eliminar informe personalizado",
    addPointGroup: "Agregar grupo personalizado (puntos)",
    editPointGroup: "Editar grupo personalizado (puntos)",
    deletePointGroup: "Eliminar grupo personalizado (puntos)",
    selectedPoints: "Puntos seleccionados",
    selectFc: "Seleccionar FC",
    selectGroupType: "Seleccionar tipo de grupo",
    groupTypes: {
      ST: "Señal remota",
      MX: "Medición remota",
      SP: "Configuración de zona única",
      SG: "Configuración de zona múltiple"
    },
    filterPlaceholder: "Filtrar por nombre/descripción",
    loadingData: "Cargando datos...",
    noDataForFc: "No hay datos para este FC",
    noDataForGroupType: "No hay datos para este tipo de grupo",
    pleaseSelectFc: "Por favor seleccione FC primero",
    pleaseSelectGroupType: "Por favor seleccione tipo de grupo primero",
    loadingGroupTypeData: "Cargando datos de tipo de grupo...",
    loadingGroupTypes: "Cargando datos de tipos de grupo...",
    loadedGroupTypes: "Tipos de grupo cargados",
    dataLoadComplete: "Carga de datos completa",
    dataLoadFailed: "Fallo en la carga de datos",
    switchingToGroupType: "Cambiando a",
    loadingGroupTypeDataSingle: "Cargando datos...",
    loadGroupTypeFailed: "Fallo al cargar datos",
    loadGroupTypeError: "Error al cargar datos",
    inputGroupName: "Por favor ingrese el nombre del grupo",
    inputGroupDesc: "Por favor ingrese la descripción del grupo",
    selectGroupTypeFirst: "Por favor seleccione tipo de grupo primero",
    menuName: "Nombre del grupo",
    menuDesc: "Descripción",
    reportName: "Nombre del informe",
    reportDesc: "Descripción",
    reportKeyword: "Palabra clave",
    reportInherit: "Heredar informe",
    inputMenuName: "Por favor ingrese el nombre del grupo",
    inputMenuDesc: "Por favor ingrese la descripción",
    inputReportName: "Por favor ingrese el nombre del informe",
    inputReportDesc: "Por favor ingrese la descripción",
    inputReportKeyword: "Por favor ingrese la palabra clave",
    selectReportInherit: "Por favor seleccione el informe a heredar",
    cancel: "Cancelar",
    confirm: "Confirmar",
    successAddMenu: "Menú personalizado agregado con éxito",
    successEditMenu: "Menú personalizado editado con éxito",
    successDeleteMenu: "Menú personalizado eliminado con éxito",
    successAddReport: "Informe personalizado agregado con éxito",
    successEditReport: "Informe personalizado editado con éxito",
    successDeleteReport: "Informe personalizado eliminado con éxito",
    successDeletePointGroup: "Grupo personalizado (puntos) eliminado con éxito",
    errorAction: "Operación fallida",
    errorDelete: "Error al eliminar",
    confirmDeleteMenu: "¿Está seguro de que desea eliminar este menú personalizado?",
    confirmDeleteReport: "¿Está seguro de que desea eliminar este informe personalizado?",
    confirmDeletePointGroup: "¿Está seguro de que desea eliminar este grupo personalizado (puntos)?",
    tip: "Sugerencia"
  },
  tree: {
    inputGroupName: "Por favor ingrese el nombre del grupo",
    expandAll: "Expandir todo",
    collapseAll: "Colapsar todo"
  }
};
