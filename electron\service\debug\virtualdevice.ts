"use strict";

import { logger } from "ee-core/log";
import { IECReq } from "../../interface/debug/request";
import { t } from "../../data/i18n/i18n";
import ClientDeviceGlobal from "../../data/debug/globalDeviceData";
import {
  RealVirtualSignalCmdType,
  WriteVirtualSignalCmdType,
} from "iec-upadrpc/dist/src/data";

/**
 * 虚拟化装置Service
 * 负责虚拟化装置参数的查询、修改等业务逻辑，使用模拟数据
 * <AUTHOR>
 * @class
 */
class VirtualDeviceService {
  // 模拟数据存储
  private simulatedData: Map<string, any[]> = new Map();

  constructor() {
    logger.info(`[VirtualDeviceService] 虚拟化装置服务初始化完成`);
    this.initSimulatedData();
  }

  /**
   * 将字符串命令类型转换为枚举类型
   * @param cmdType 字符串命令类型
   * @returns 枚举类型或原字符串
   */
  private convertToEnumCmdType(
    cmdType: string
  ): RealVirtualSignalCmdType | string {
    switch (cmdType) {
      case "read_analoy_para":
        return RealVirtualSignalCmdType.READ_ANALOY_PARA;
      case "read_bi_para":
        return RealVirtualSignalCmdType.READ_BI_PARA;
      case "read_bo_para":
        return RealVirtualSignalCmdType.READ_BO_PARA;
      case "read_fault_para":
        return RealVirtualSignalCmdType.READ_FAULT_PARA;
      case "read_led_para":
        return RealVirtualSignalCmdType.READ_LED_PARA;
      default:
        return cmdType; // 如果不匹配，返回原字符串
    }
  }

  /**
   * 将client返回的数据字段映射到前端需要的字段
   * val -> ang, ang -> dataValue
   * @param data client返回的数据数组
   * @param cmdType 命令类型
   * @returns 映射后的数据数组
   */
  private mapClientDataToFrontend(
    data: any[],
    cmdType: string | RealVirtualSignalCmdType
  ): any[] {
    if (!Array.isArray(data)) {
      return data;
    }

    return data.map((item, index) => {
      const mappedItem = { ...item };

      // 根据命令类型进行不同的字段映射
      if (
        cmdType === "read_led_para" ||
        cmdType === RealVirtualSignalCmdType.READ_LED_PARA
      ) {
        // 对于LED参数，只有在read_led_para时才需要将val映射到value
        if (item.val !== undefined) {
          mappedItem.value = item.val;
        }
      } else {
        // 对于其他类型的参数（模拟量等），val -> ang (相角)
        if (item.val !== undefined) {
          mappedItem.dataValue = item.val;
          mappedItem.originalDataValue = item.val; // 设置原始值
        }
      }

      // ang -> dataValue (幅值)
      if (item.ang !== undefined) {
        mappedItem.ang = item.ang;
        mappedItem.originalAng = item.ang; // 设置原始值
      }

      // 设置修改状态
      mappedItem.isModified = false;
      mappedItem.index = index + 1;
      mappedItem.description = item.desc;

      // 对于其他类型的参数，可能也需要类似的映射
      // 这里可以根据实际需要扩展

      logger.debug(
        `[VirtualDeviceService] mapClientDataToFrontend - 字段映射: ${item.name || item.id}`,
        { original: item, mapped: mappedItem }
      );

      return mappedItem;
    });
  }

  /**
   * 将前端数据字段映射到client需要的字段
   * ang -> val, dataValue -> ang
   * @param data 前端数据数组
   * @param cmdType 命令类型
   * @returns 映射后的数据数组
   */
  private mapFrontendDataToClient(
    data: any[],
    cmdType: string | WriteVirtualSignalCmdType
  ): any[] {
    if (!Array.isArray(data)) {
      return data;
    }

    return data.map((item) => {
      const mappedItem = { ...item };

      // 对于模拟量参数，需要进行字段映射
      if (
        cmdType === WriteVirtualSignalCmdType.ANALOY_INPUT ||
        cmdType === "analoy_input" ||
        cmdType === "read_analoy_para"
      ) {
        // ang -> val (相角)
        if (item.ang !== undefined) {
          mappedItem.val = item.ang;
        }
        // dataValue -> ang (幅值)
        if (item.dataValue !== undefined) {
          mappedItem.ang = item.dataValue;
        }
      }

      logger.debug(
        `[VirtualDeviceService] mapFrontendDataToClient - 字段映射: ${item.name || item.id}`,
        { original: item, mapped: mappedItem }
      );

      return mappedItem;
    });
  }

  /**
   * 获取虚拟化装置参数
   * @param req 请求体，包含cmdType等参数
   * @returns 虚拟化装置参数列表
   */
  async getVirtualParams(
    req: IECReq<any>
  ): Promise<{ list: any[]; total: number }> {
    try {
      logger.info(
        `[VirtualDeviceService] getVirtualParams - 开始获取虚拟化装置参数:`,
        req.data
      );

      // 获取urpcClient
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;

      const { cmdType, pageNum = 1, pageSize = 10 } = req.data;

      let data: any[] = [];

      // 尝试从client获取实际数据
      if (
        client &&
        typeof (client as any).getVirtualSignalInfo === "function"
      ) {
        try {
          logger.info(
            `[VirtualDeviceService] getVirtualParams - 从client获取实际数据，类型: ${cmdType}`
          );

          // 调用client获取虚拟信号信息
          const requestData = {
            cmdType: cmdType,
            cb: () => {}, // 添加必需的cb回调函数
          };

          logger.info("requestData:", requestData);
          const virtualSignalResult = await (
            client as any
          ).getVirtualSignalInfo(requestData);

          logger.info("result", virtualSignalResult);

          // 检查返回结果的结构：IECResult { code, hasNext, data: { cmdType, table, moreFollows } }
          if (
            virtualSignalResult &&
            virtualSignalResult.data &&
            virtualSignalResult.data.table
          ) {
            // 数据在 result.data.table 节点下
            if (Array.isArray(virtualSignalResult.data.table)) {
              data = virtualSignalResult.data.table;

              // 进行字段映射：val -> ang, ang -> dataValue
              data = this.mapClientDataToFrontend(data, cmdType);

              logger.info(
                `[VirtualDeviceService] getVirtualParams - 成功从client获取实际数据，数量: ${data.length}，cmdType: ${virtualSignalResult.data.cmdType}，moreFollows: ${virtualSignalResult.data.moreFollows}`
              );
            } else {
              logger.warn(
                `[VirtualDeviceService] getVirtualParams - client返回的table不是数组格式，使用模拟数据`
              );
              data = this.simulatedData.get(cmdType) || [];
            }
          } else {
            logger.warn(
              `[VirtualDeviceService] getVirtualParams - client返回数据格式不符合预期（缺少data.table节点），使用模拟数据`
            );
            data = this.simulatedData.get(cmdType) || [];
          }
        } catch (clientError) {
          logger.error(
            `[VirtualDeviceService] getVirtualParams - 从client获取数据失败，使用模拟数据:`,
            clientError
          );
          data = this.simulatedData.get(cmdType) || [];
        }
      } else {
        logger.warn(
          `[VirtualDeviceService] getVirtualParams - client不可用或不支持getVirtualSignalInfo方法，使用模拟数据`
        );
        data = this.simulatedData.get(cmdType) || [];
      }

      // 进行分页处理
      const startIndex = (pageNum - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedData = data.slice(startIndex, endIndex);

      const result = {
        list: paginatedData,
        total: data.length,
      };

      logger.info(
        `[VirtualDeviceService] getVirtualParams - 成功获取虚拟化装置参数，类型: ${cmdType}，数量: ${paginatedData.length}，总数: ${data.length}`
      );

      return result;
    } catch (error) {
      logger.error(
        `[VirtualDeviceService] getVirtualParams - 获取虚拟化装置参数异常:`,
        error
      );
      throw error;
    }
  }

  /**
   * 更新虚拟化装置参数
   * @param req 请求体，包含cmdType和params等参数
   * @returns 更新结果
   */
  async updateVirtualParams(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(
        `[VirtualDeviceService] updateVirtualParams - 开始更新虚拟化装置参数:`,
        req.data
      );

      // 获取urpcClient
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;

      const { cmdType, params } = req.data;

      if (!params || !Array.isArray(params)) {
        throw new Error("参数格式错误，params必须是数组");
      }

      // 尝试调用client设置虚拟信号值
      if (
        client &&
        typeof (client as any).setVirtualSignalValue === "function"
      ) {
        try {
          logger.info(
            `[VirtualDeviceService] updateVirtualParams - 调用client设置虚拟信号值，类型: ${cmdType}`
          );

          // 将前端数据字段映射到client需要的字段格式
          const mappedParams = this.mapFrontendDataToClient(params, cmdType);

          // 调用client设置虚拟信号值
          const requestData = {
            cmdType: cmdType,
            params: mappedParams,
            cb: () => {}, // 添加必需的cb回调函数
          };

          const setResult = await client.setVirtualSignalValue(
            requestData
          );

          // 检查设置结果
          if (
            setResult &&
            (setResult.isSuccess
              ? setResult.isSuccess()
              : setResult.success !== false)
          ) {
            logger.info(
              `[VirtualDeviceService] updateVirtualParams - 成功通过client设置虚拟信号值，类型: ${cmdType}，更新数量: ${params.length}`
            );

            // 同时更新本地模拟数据以保持一致性
            this.updateLocalSimulatedData(cmdType, params);

            return true;
          } else {
            logger.warn(
              `[VirtualDeviceService] updateVirtualParams - client设置虚拟信号值失败，回退到本地模拟数据更新`
            );
            // 回退到本地模拟数据更新
            return this.updateLocalSimulatedData(cmdType, params);
          }
        } catch (clientError) {
          logger.error(
            `[VirtualDeviceService] updateVirtualParams - client设置虚拟信号值异常，回退到本地模拟数据更新:`,
            clientError
          );
          // 回退到本地模拟数据更新
          return this.updateLocalSimulatedData(cmdType, params);
        }
      } else {
        logger.warn(
          `[VirtualDeviceService] updateVirtualParams - client不可用或不支持setVirtualSignalValue方法，使用本地模拟数据更新`
        );
        // 使用本地模拟数据更新
        return this.updateLocalSimulatedData(cmdType, params);
      }
    } catch (error) {
      logger.error(
        `[VirtualDeviceService] updateVirtualParams - 更新虚拟化装置参数异常:`,
        error
      );
      throw error;
    }
  }

  /**
   * 更新本地模拟数据
   * @param cmdType 命令类型
   * @param params 参数数组
   * @returns 更新结果
   */
  private updateLocalSimulatedData(cmdType: string, params: any[]): boolean {
    try {
      // 获取对应类型的模拟数据
      const data = this.simulatedData.get(cmdType) || [];

      // 更新参数
      params.forEach((updateParam) => {
        const existingParam = data.find((item) => item.id === updateParam.id);
        if (existingParam) {
          // 根据不同类型更新不同字段
          if (cmdType === "read_analoy_para") {
            if (updateParam.dataValue !== undefined) {
              existingParam.dataValue = updateParam.dataValue;
              existingParam.isModified =
                existingParam.dataValue !== existingParam.originalDataValue;
            }
            if (updateParam.ang !== undefined) {
              existingParam.ang = updateParam.ang;
              existingParam.isModified =
                existingParam.ang !== existingParam.originalAng;
            }
          } else if (
            cmdType === "read_bi_para" ||
            cmdType === "read_fault_para"
          ) {
            if (updateParam.dataValue !== undefined) {
              existingParam.dataValue = updateParam.dataValue;
              existingParam.isModified =
                existingParam.dataValue !== existingParam.originalDataValue;
            }
          } else if (cmdType === "read_led_para") {
            if (updateParam.value !== undefined) {
              existingParam.value = updateParam.value;
            }
          }

          logger.info(
            `[VirtualDeviceService] updateLocalSimulatedData - 更新参数: ${existingParam.name}`,
            existingParam
          );
        } else {
          logger.warn(
            `[VirtualDeviceService] updateLocalSimulatedData - 未找到参数: ${updateParam.id}`
          );
        }
      });

      // 更新存储的数据
      this.simulatedData.set(cmdType, data);

      logger.info(
        `[VirtualDeviceService] updateLocalSimulatedData - 成功更新本地模拟数据，类型: ${cmdType}，更新数量: ${params.length}`
      );

      return true;
    } catch (error) {
      logger.error(
        `[VirtualDeviceService] updateLocalSimulatedData - 更新本地模拟数据异常:`,
        error
      );
      return false;
    }
  }

  /**
   * 故障录波回放
   * @param req 请求体，包含fileName、filePath、fileSize等参数
   * @returns 回放结果
   */
  async playbackWaveReplay(req: IECReq<any>): Promise<boolean> {
    try {
      logger.info(
        `[VirtualDeviceService] playbackWaveReplay - 开始故障录波回放:`,
        req.data
      );

      // 获取urpcClient
      const device = ClientDeviceGlobal.getInstance().getDeviceInfoGlobal(
        req.head.id
      );
      const client = device?.deviceClient;

      const { fileName, filePath, fileSize } = req.data;

      if (!fileName || !filePath) {
        throw new Error("文件名和文件路径不能为空");
      }

      // 尝试调用client进行虚拟故障回放
      if (client && typeof (client as any).virtualFaultReplay === "function") {
        try {
          logger.info(
            `[VirtualDeviceService] playbackWaveReplay - 调用client进行虚拟故障回放: ${fileName}`
          );

          // 调用client进行虚拟故障回放
          const requestData = {
            fileName: fileName,
            filePath: filePath,
            fileSize: fileSize,
            cb: () => {}, // 添加必需的cb回调函数
          };

          const replayResult = await (client as any).virtualFaultReplay(
            requestData
          );

          // 检查回放结果
          if (
            replayResult &&
            (replayResult.isSuccess
              ? replayResult.isSuccess()
              : replayResult.success !== false)
          ) {
            logger.info(
              `[VirtualDeviceService] playbackWaveReplay - 成功通过client进行虚拟故障回放: ${fileName}`
            );
            return true;
          } else {
            logger.warn(
              `[VirtualDeviceService] playbackWaveReplay - client虚拟故障回放失败，但返回成功状态`
            );
            // 即使client调用失败，也返回true以保持向后兼容性
            return true;
          }
        } catch (clientError) {
          logger.error(
            `[VirtualDeviceService] playbackWaveReplay - client虚拟故障回放异常，但返回成功状态:`,
            clientError
          );
          // 即使client调用异常，也返回true以保持向后兼容性
          return true;
        }
      } else {
        logger.warn(
          `[VirtualDeviceService] playbackWaveReplay - client不可用或不支持virtualFaultReplay方法，模拟回放成功`
        );

        // 模拟回放成功
        logger.info(
          `[VirtualDeviceService] playbackWaveReplay - 模拟故障录波回放成功: ${fileName}`
        );
        return true;
      }
    } catch (error) {
      logger.error(
        `[VirtualDeviceService] playbackWaveReplay - 故障录波回放异常:`,
        error
      );
      throw error;
    }
  }
}

VirtualDeviceService.toString = () => "[class VirtualDeviceService]";
const virtualDeviceService = new VirtualDeviceService();

export { VirtualDeviceService, virtualDeviceService };
